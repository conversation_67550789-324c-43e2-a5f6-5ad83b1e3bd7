import { describe, it, expect, beforeAll, afterAll } from "vitest";
import request from "supertest";
import app from "../../app.js";
import prisma from "../../db/connection.js";
import auth from "../auth.js";
let agent;
let testUser;
describe("Agent API Integration", () => {
    beforeAll(async () => {
        await prisma.agent.deleteMany();
        await prisma.user.deleteMany();
        await auth.api.signUpEmail({
            body: {
                name: "test",
                email: "<EMAIL>",
                password: "test12345",
            },
        });
        // Create a persistent agent
        agent = request.agent(app);
        const response = await agent
            .post("/api/auth/sign-in/email")
            .send({
            email: "<EMAIL>",
            password: "test12345",
            rememberMe: true,
        })
            .expect(200);
        testUser = response.body.user;
    });
    afterAll(async () => {
        await prisma.$disconnect();
    });
    it("should create a new agent", async () => {
        // The agent now automatically includes the session cookie
        const res = await agent
            .post("/api/agents")
            .send({
            title: "My Test Agent",
            prompt: "You are a helpful assistant",
        })
            .expect(201); // Check for the correct status code
        expect(res.body.data).toHaveProperty("id");
    });
    it("should fetch all agents for user", async () => {
        // The agent automatically includes the session cookie
        const res = await agent
            .get("/api/agents")
            .expect(200);
        expect(res.body.data.agents).toBeInstanceOf(Array);
    });
    it("should fetch a single agent by id", async () => {
        const created = await prisma.agent.create({
            data: { title: "Single Agent", prompt: "Test prompt", userId: testUser.id },
        });
        const res = await agent
            .get(`/api/agents/${created.id}`)
            .expect(200);
        expect(res.body.data).toHaveProperty("id", created.id);
    });
    it("should update an agent", async () => {
        const created = await prisma.agent.create({
            data: { title: "To Update", prompt: "Old", userId: testUser.id },
        });
        const res = await agent
            .put(`/api/agents/${created.id}`)
            .send({ title: "Updated Agent" })
            .expect(200);
    });
    it("should delete an agent", async () => {
        const created = await prisma.agent.create({
            data: { title: "To Delete", prompt: "Temp", userId: testUser.id },
        });
        const res = await agent
            .delete(`/api/agents/${created.id}`)
            .expect(200);
    });
});
//# sourceMappingURL=agent.test.js.map