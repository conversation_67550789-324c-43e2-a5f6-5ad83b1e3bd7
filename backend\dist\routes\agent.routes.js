import { Router } from "express";
import { authMiddleware } from "../middleware/auth.middleware.js";
import { createAgent, listAgents, getAgent, updateAgent, deleteAgent } from "../controllers/agent.controller.js";
const router = Router();
router.post("/", authMiddleware, createAgent);
router.get("/", authMiddleware, listAgents);
router.get("/:id", authMiddleware, getAgent);
router.put("/:id", authMiddleware, updateAgent);
router.delete("/:id", authMiddleware, deleteAgent);
export default router;
//# sourceMappingURL=agent.routes.js.map