import { Router } from "express";
import { authMiddleware } from "../middleware/auth.middleware.js";
import { createMeeting, getMeetingById, updateMeeting, getMeetingsScheduledForUser, getMeetingsScheduledByUser, deleteMeeting, updateMeetingStatus, joinMeeting, } from "../controllers/meeting.controller.js";
const router = Router();
router.get("/scheduled/for", authMiddleware, getMeetingsScheduledForUser);
router.get("/scheduled/by", authMiddleware, getMeetingsScheduledByUser);
router.put("/:id/status", authMiddleware, updateMeetingStatus);
router.post("/:id/join", authMiddleware, joinMeeting);
router.post("/", authMiddleware, createMeeting);
router.get("/:id", authMiddleware, getMeetingById);
router.put("/:id", authMiddleware, updateMeeting);
router.delete("/:id", authMiddleware, deleteMeeting);
export default router;
//# sourceMappingURL=meeting.routes.js.map