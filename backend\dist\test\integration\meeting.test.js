import { describe, it, expect, beforeAll, afterAll } from "vitest";
import request from "supertest";
import app from "../../app.js";
import prisma from "../../db/connection.js";
import auth from "../auth.js";
import { initializeLivekitService } from "../../utils/livekit.js";
let agentA;
let agentB;
let userA; // scheduler (signed-in user)
let userB; // scheduled for
let createdAgent; // agent owned by userA
let meeting; // created meeting
describe("Meeting API Integration", () => {
    beforeAll(async () => {
        // Clean state
        await prisma.meeting.deleteMany();
        await prisma.agent.deleteMany();
        await prisma.user.deleteMany();
        initializeLivekitService();
        // Sign up two users
        await auth.api.signUpEmail({
            body: { name: "userA", email: "<EMAIL>", password: "test12345" },
        });
        await auth.api.signUpEmail({
            body: { name: "user<PERSON>", email: "<EMAIL>", password: "test12345" },
        });
        // Create persistent agents (supertest) for each user session
        agentA = request.agent(app);
        agentB = request.agent(app);
        // Sign in userA and capture returned user for IDs
        const resA = await agentA
            .post("/api/auth/sign-in/email")
            .send({ email: "<EMAIL>", password: "test12345", rememberMe: true })
            .expect(200);
        userA = resA.body.user;
        // Sign in userB (for scheduled/for tests)
        await agentB
            .post("/api/auth/sign-in/email")
            .send({ email: "<EMAIL>", password: "test12345", rememberMe: true })
            .expect(200);
        // Create an agent for userA via API so it carries correct ownership
        const agentCreate = await agentA
            .post("/api/agents")
            .send({ title: "Meeting Agent", prompt: "Assist with meetings" })
            .expect(201);
        createdAgent = agentCreate.body.data;
        // Resolve userB (scheduledFor) from DB
        userB = await prisma.user.findUnique({ where: { email: "<EMAIL>" } });
    });
    afterAll(async () => {
        await prisma.$disconnect();
    });
    it("should create a new meeting", async () => {
        const res = await agentA
            .post("/api/meetings")
            .send({ agentId: createdAgent.id, forUser: userB.id })
            .expect(200);
        expect(res.body.data).toHaveProperty("id");
        meeting = res.body.data;
    });
    it("should fetch a meeting by id", async () => {
        const res = await agentA
            .get(`/api/meetings/${meeting.id}`)
            .expect(200);
        expect(res.body.data).toHaveProperty("id", meeting.id);
    });
    it("should update a meeting (change agent)", async () => {
        // Create another agent for userA
        const another = await agentA
            .post("/api/agents")
            .send({ title: "Another Agent", prompt: "New prompt" })
            .expect(201);
        const res = await agentA
            .put(`/api/meetings/${meeting.id}`)
            .send({ agentId: another.body.data.id })
            .expect(200);
        expect(res.body.message).toBe("Meeting updated successfully.");
    });
    it("should list meetings scheduled by the signed-in user", async () => {
        const res = await agentA.get("/api/meetings/scheduled/by").expect(200);
        expect(res.body.data).toBeInstanceOf(Array);
        expect(res.body.data.length).toBeGreaterThanOrEqual(1);
    });
    it("should list meetings scheduled for the other user", async () => {
        const res = await agentB.get("/api/meetings/scheduled/for").expect(200);
        expect(res.body.data).toBeInstanceOf(Array);
        // Optional: ensure our meeting appears for userB
        const ids = res.body.data.map((m) => m.id);
        expect(ids).toContain(meeting.id);
    });
    it("should update meeting status", async () => {
        const res = await agentA
            .put(`/api/meetings/${meeting.id}/status`)
            .send({ status: "COMPLETED" })
            .expect(200);
        expect(res.body.message).toBe("Meeting status updated successfully.");
        expect(res.body.data.status).toBe("COMPLETED");
    });
    it("should join a meeting (create room and return jwt token)", async () => {
        const res = await agentA
            .post(`/api/meetings/${meeting.id}/join`)
            .expect(200);
        console.log(res);
        expect(res.body.message).toBe("Room created successfully.");
        expect(res.body.data).toHaveProperty("jwtToken");
    });
    it("should delete a meeting", async () => {
        await agentA.delete(`/api/meetings/${meeting.id}`).expect(200);
    });
});
//# sourceMappingURL=meeting.test.js.map