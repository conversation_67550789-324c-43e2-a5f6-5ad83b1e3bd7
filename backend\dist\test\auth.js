import { prismaAdapter } from "better-auth/adapters/prisma";
import { betterAuth } from "better-auth";
import { bearer } from "better-auth/plugins/bearer";
import prisma from "../db/connection.js";
const auth = betterAuth({
    secret: "super-secret",
    database: prismaAdapter(prisma, {
        provider: "postgresql"
    }),
    plugins: [bearer()],
    emailAndPassword: { enabled: true },
    rateLimit: { enabled: false },
    advanced: { disableCSRFCheck: true, cookies: {} },
});
export default auth;
//# sourceMappingURL=auth.js.map