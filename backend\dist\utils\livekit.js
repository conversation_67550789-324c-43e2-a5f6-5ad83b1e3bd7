import { RoomServiceClient, AccessToken, Room } from 'livekit-server-sdk';
import { LIVEKIT_API_KEY, LIVEKIT_API_SECRET, LIVEKIT_URL } from "../constant.js";
let roomService;
export const initializeLivekitService = () => {
    try {
        roomService = new RoomServiceClient(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET);
        console.log("Livekit room service initialized successfully");
    }
    catch (error) {
        console.log(error);
        console.log("Error while initializing room service");
        process.exit(1);
    }
};
export const createLivekitRoom = async (roomName, metadata) => {
    try {
        const opts = {
            name: roomName,
            emptyTimeout: 60 * 30,
            maxParticipants: 4,
            departureTimeout: 60 * 5,
            metadata: JSON.stringify(metadata),
        };
        const token = generateAccessToken(roomName);
        token.addGrant({
            room: roomName,
            roomJoin: true,
            canPublish: false,
            canSubscribe: true
        });
        const jwtToken = await token.toJwt();
        const room = await roomService?.createRoom(opts);
        return { room, jwtToken };
    }
    catch (error) {
        console.log(error);
        throw error;
    }
};
const generateAccessToken = (meetingId) => {
    return new AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET, {
        identity: meetingId,
        ttl: 35 * 60
    });
};
//# sourceMappingURL=livekit.js.map