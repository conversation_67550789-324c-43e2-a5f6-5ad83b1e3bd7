import z from "zod";
export const removeUndefined = (data) => {
    const newObject = {};
    for (const key in data) {
        if (data[key] !== undefined) {
            newObject[key] = data[key];
        }
    }
    return newObject;
};
export const cleanZodError = (error) => {
    try {
        return z.prettifyError(error);
    }
    catch (error) {
        return "Error";
    }
};
//# sourceMappingURL=helper.js.map